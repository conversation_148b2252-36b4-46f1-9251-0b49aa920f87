import type {Config} from 'tailwindcss';

const config: Config = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    '*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    // // Set Arial as the default font family for all text
    // fontFamily: {
    //   sans: ['Arial', 'Helvetica', 'sans-serif'],
    // },
    // extend: {
    //   // Custom fonts
    //   fontFamily: {
    //   },
    //   // Your custom colors - direct hex values for maximum performance
    //   colors: {
    //   },
    // },
  },
  // Keep tailwindcss-animate for shadcn/ui components
  // eslint-disable-next-line @typescript-eslint/no-require-imports -- config file
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;

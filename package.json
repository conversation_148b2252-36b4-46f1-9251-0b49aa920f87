{"name": "software-maturity-models", "version": "1.0.0", "private": true, "sideEffects": false, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b", "engines": {"node": ">=22", "pnpm": ">=10", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "prod": "next build && next start", "analyze": "ANALYZE=true next build", "start": "next start", "typecheck": "tsc --noEmit", "lint": "next lint --fix", "validate": "pnpm typecheck && pnpm lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "4.1.0", "geist": "^1.4.2", "lucide-react": "^0.542.0", "next": "^15.5.2", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "9.9.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.4", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "3.25.67"}, "devDependencies": {"@eslint/js": "^9.34.0", "@next/bundle-analyzer": "^15.5.2", "@next/eslint-plugin-next": "^15.5.2", "@stylistic/eslint-plugin": "^5.2.3", "@types/node": "^22.18.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "eslint": "^9.34.0", "eslint-config-next": "15.3.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0"}, "browserslist": ["Chrome >= 109", "Firefox >= 109", "Safari >= 16.4", "Edge >= 109"]}